## 1. 前置准备

### 先决条件

- 安装 Java 8 或更高版本
  ```bash
  # 检查 Java 版本
  java -version
  ```
- 安装 Clojure 和 Leiningen 构建工具
  ```bash
  # macOS
  brew install clojure/tools/clojure leiningen
  
  # Linux
  curl -O https://download.clojure.org/install/linux-install-1.11.1.1413.sh
  chmod +x linux-install-1.11.1.1413.sh
  sudo ./linux-install-1.11.1.1413.sh
  ```
- 拥有 Datomic 数据库访问权限
- 获取 Datomic URI 连接字符串

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `DATOMIC_URI`: 您的 Datomic 数据库连接 URI（如：datomic:dev://localhost:4334/your-db）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。
