## 1. Setup & Requirements

### Prerequisites

- Install Java 8 or higher
  ```bash
  # Check Java version
  java -version
  ```
- Install Clojure and Leiningen build tools
  ```bash
  # macOS
  brew install clojure/tools/clojure leiningen
  
  # Linux
  curl -O https://download.clojure.org/install/linux-install-1.11.1.1413.sh
  chmod +x linux-install-1.11.1.1413.sh
  sudo ./linux-install-1.11.1.1413.sh
  ```
- Have access to a Datomic database
- Obtain Datomic URI connection string

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `DATOMIC_URI`: Your Datomic database connection URI (e.g., datomic:dev://localhost:4334/your-db)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
